import { db, query, getOne, getAll } from './sqlite-db.js';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the root .env file
const envPath = path.join(__dirname, '..', '.env');
dotenv.config({ path: envPath });

console.log('Auth module - Loading environment variables from:', envPath);
console.log('Auth module - JWT_SECRET exists:', !!process.env.JWT_SECRET);

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_here';
const TOKEN_EXPIRY = '7d'; // Token expires in 7 days

// Hash a password
const hashPassword = (password) => {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
};

// Verify a password against a hash
const verifyPassword = (password, hashedPassword) => {
  const [salt, hash] = hashedPassword.split(':');
  const calculatedHash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return hash === calculatedHash;
};

// Generate a JWT token
const generateToken = (userId) => {
  return jwt.sign({ sub: userId }, JWT_SECRET, { expiresIn: TOKEN_EXPIRY });
};

// Verify a JWT token
const verifyToken = (token) => {
  try {
    console.log('Verifying token with JWT_SECRET:', JWT_SECRET ? 'Secret exists' : 'No secret');
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('Token verified successfully, user ID:', decoded.sub);
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error.message);
    return null;
  }
};

// Register a new user
const registerUser = async (email, password, fullName) => {
  try {
    // Check if user already exists
    const existingUser = getOne('SELECT * FROM users WHERE email = ?', [email]);

    if (existingUser) {
      return { error: { message: 'User already exists' } };
    }

    // Generate a new UUID for the user
    const userId = uuidv4();
    const hashedPassword = hashPassword(password);

    // Create profile first (parent table)
    db.prepare(`
      INSERT INTO profiles (id, full_name, plan, posts_count, posts_limit, last_post_time)
      VALUES (?, ?, ?, ?, ?, ?)
    `).run(userId, fullName, 'free', 0, 3, null);

    // Create user (child table)
    db.prepare(`
      INSERT INTO users (id, email, password_hash)
      VALUES (?, ?, ?)
    `).run(userId, email, hashedPassword);

    // Generate token
    const token = generateToken(userId);

    // Get the created profile
    const profile = getOne('SELECT * FROM profiles WHERE id = ?', [userId]);
    console.log('Profile data being returned:', profile);

    return {
      data: {
        user: {
          id: userId,
          email,
          profile
        },
        token
      },
      error: null
    };
  } catch (error) {
    console.error('Error registering user:', error);
    return { error: { message: 'Registration failed' } };
  }
};

// Login a user
const loginUser = async (email, password) => {
  try {
    // Get user by email
    const user = getOne(`
      SELECT u.*, p.full_name, p.plan, p.posts_count, p.posts_limit
      FROM users u
      JOIN profiles p ON u.id = p.id
      WHERE u.email = ?
    `, [email]);

    if (!user) {
      return { error: { message: 'Invalid email or password' } };
    }

    // Verify password
    if (!verifyPassword(password, user.password_hash)) {
      return { error: { message: 'Invalid email or password' } };
    }

    // Generate token
    const token = generateToken(user.id);

    // Return user data and token
    return {
      data: {
        user: {
          id: user.id,
          email: user.email,
          profile: {
            id: user.id,
            full_name: user.full_name,
            plan: user.plan,
            posts_count: user.posts_count,
            posts_limit: user.posts_limit
          }
        },
        token
      },
      error: null
    };
  } catch (error) {
    console.error('Error logging in:', error);
    return { error: { message: 'Login failed' } };
  }
};

// Get user profile by ID
const getUserProfile = async (userId, forceRefresh = false) => {
  try {
    // Always fetch directly from the database
    const profile = getOne('SELECT * FROM profiles WHERE id = ?', [userId]);
    console.log('getUserProfile - Profile data:', profile);

    if (!profile) {
      return { error: { message: 'Profile not found' } };
    }

    // Check if cancelled subscription has expired and should be downgraded
    if (profile.subscription_status === 'cancelled' && 
        profile.next_billing_date && 
        new Date(profile.next_billing_date) <= new Date()) {
      
      console.log(`User ${userId} cancelled subscription has expired, downgrading to free plan`);
      
      // Downgrade to free plan
      const downgradedProfile = {
        ...profile,
        plan: 'free',
        posts_limit: 3,
        posts_count: 0,
        subscription_status: 'expired',
        billing_cycle: null,
        next_billing_date: null,
        paypal_subscription_id: null,
        updated_at: new Date().toISOString()
      };

      // Update in database
      const updateStmt = db.prepare(`
        UPDATE profiles 
        SET plan = ?, posts_limit = ?, posts_count = ?, subscription_status = ?, 
            billing_cycle = ?, next_billing_date = ?, paypal_subscription_id = ?, updated_at = ?
        WHERE id = ?
      `);
      
      updateStmt.run(
        'free', 3, 0, 'expired', 
        null, null, null, downgradedProfile.updated_at, 
        userId
      );

      return { data: downgradedProfile, error: null };
    }

    // If this is a force refresh, log it
    if (forceRefresh) {
      console.log(`Force refreshing profile for user ${userId}`);
    }

    // Check if profile has [PLAN UPDATED] flag and clean it automatically
    if (profile.full_name && profile.full_name.includes('[PLAN UPDATED]')) {
      console.log('Cleaning [PLAN UPDATED] flag from profile');
      const cleanName = profile.full_name.replace(/\s*\[PLAN UPDATED\]\s*/g, '').trim();
      
      // Update the profile to remove the flag
      const stmt = db.prepare('UPDATE profiles SET full_name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?');
      stmt.run(cleanName, userId);
      
      // Update the profile object to return the clean name
      profile.full_name = cleanName;
      profile.updated_at = new Date().toISOString();
    }

    return { data: profile, error: null };
  } catch (error) {
    console.error('Error getting user profile:', error);
    return { error: { message: 'Failed to get profile' } };
  }
};

// Update user profile
const updateUserProfile = async (userId, updates) => {
  try {
    const allowedFields = [
      'full_name', 
      'plan', 
      'posts_count', 
      'posts_limit',
      'subscription_status',
      'billing_cycle',
      'next_billing_date',
      'paypal_subscription_id',
      'paypal_order_id',
      'updated_at',
      'is_admin'
    ];
    const fields = Object.keys(updates).filter(key => allowedFields.includes(key));

    if (fields.length === 0) {
      return { error: { message: 'No valid fields to update' } };
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => updates[field]);
    values.push(userId);

    const stmt = db.prepare(`UPDATE profiles SET ${setClause} WHERE id = ?`);
    const result = stmt.run(...values);

    if (result.changes === 0) {
      return { error: { message: 'Profile not found' } };
    }

    // Get updated profile
    return await getUserProfile(userId);
  } catch (error) {
    console.error('Error updating user profile:', error);
    return { error: { message: 'Failed to update profile' } };
  }
};

export {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  verifyToken,
  generateToken
};
